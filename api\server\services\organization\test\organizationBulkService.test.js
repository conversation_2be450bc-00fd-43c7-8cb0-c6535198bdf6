const chai = require('chai');
const chaiHttp = require('chai-http');
const expect = chai.expect;
const assert = chai.assert;
const request = require('supertest');
chai.use(chaiHttp);
const sinon = require('sinon');
const User = require('../../../models/user.model');
const Organization = require('../../../models/organization.model');
const jwt = require('jsonwebtoken');
const Utils = require('../../../util/utilFunctions');
const { afterEach, beforeEach } = require('mocha');
const organizationMemberModel = require('../../../models/organizationMember.model');

const tokenOptionalInfo = {
    algorithm: 'HS256',
    expiresIn: 86400
};
/* User Token */
const user = {
    id: '*************-4b2e-b36b-898597b8146e',
    sub: '*************-4b2e-b36b-898597b8146e',
    email: '<EMAIL>',
    isVerified: 1,
    role: 4,
    status: 'active',
    isDeleted: 0,
    accessLevel: CONSTANTS.ACCESS_LEVEL.ROOT
};
const requestPayloadUser = {
    token: 'Bearer ' + jwt.sign(user, 'testToken', tokenOptionalInfo)
};
const organization = {
    orgName: 'Test Organization',
    adminFirstName: 'Test',
    adminLastName: 'Name',
    category: 'PTO',
    address: 'Organization test address',
    country: 'country',
    city: 'city',
    state: 'state',
    zipCode: '12345',
    phoneNumber: '1111111111',
    countryCode: '+1',
    email: '<EMAIL>',
    allowedPaymentType: {
        cash: true,
        cheque: false,
        stripe: false,
        venmo: false
    },
    parentOrganization: '*************-4b2e-b36b-898597b8146f',
    associatedOrganizations: ['org-1', 'org-2', 'org-3']
};

Utils.addCommonReqTokenForHMac(request);

describe('Get Associated Organizations', () => {
    try {
        let getStub;
        beforeEach(async () => {
            getStub = sinon.stub(User, 'get');
        });

        afterEach(() => {
            sinon.restore();
        });

        it('Should validate if organizationId is passed', (done) => {
            getStub.resolves({ status: 'active', isVerified: 1, role: 4, accessLevel: CONSTANTS.ACCESS_LEVEL.ROOT });

            request(process.env.BASE_URL)
                .get('/organization/associatedOrganizations')
                .query()
                .set({ Authorization: requestPayloadUser.token })
                .end((err, res) => {
                    expect(res.body.status).to.be.status;
                    assert.equal(res.statusCode, 400);
                    assert.equal(res.body.message, 'Organization Id is required');
                    done();
                });
        });

        it('should validate if organizationId is uuid', (done) => {
            getStub.resolves({ status: 'active', isVerified: 1, role: 4, accessLevel: CONSTANTS.ACCESS_LEVEL.ROOT });

            request(process.env.BASE_URL)
                .get('/organization/associatedOrganizations')
                .query({ orgId: 'invalid-uuid' })
                .set({ Authorization: requestPayloadUser.token })
                .end((err, res) => {
                    expect(res.body.status).to.be.status;
                    assert.equal(res.statusCode, 400);
                    assert.equal(res.body.message, 'Organization Id is not valid.');
                    done();
                });
        });

        it('should throw error if organization is not found', (done) => {
            getStub.resolves({ status: 'active', isVerified: 1, role: 4, accessLevel: CONSTANTS.ACCESS_LEVEL.ROOT });

            sinon.stub(Organization, 'get').resolves(null);

            request(process.env.BASE_URL)
                .get('/organization/associatedOrganizations')
                .query({ orgId: '*************-4b2e-b36b-898597b8146f' })
                .set({ Authorization: requestPayloadUser.token })
                .end((err, res) => {
                    expect(res.body.status).to.be.status;
                    assert.equal(res.statusCode, 400);
                    assert.equal(res.body.message, 'Organization with given id does not exist');
                    done();
                });
        });

        it('should return organization details with empty associated organizations array', (done) => {
            getStub.resolves({ status: 'active', isVerified: 1, role: 4, accessLevel: CONSTANTS.ACCESS_LEVEL.ROOT });

            sinon.stub(Organization, 'get').resolves({ ...organization, associatedOrganizations: [null] });

            request(process.env.BASE_URL)
                .get('/organization/associatedOrganizations')
                .query({ orgId: '*************-4b2e-b36b-898597b8146f' })
                .set({ Authorization: requestPayloadUser.token })
                .end((err, res) => {
                    expect(res.body.status).to.be.status;
                    assert.equal(res.statusCode, 200);
                    assert.equal(res.body.data.associatedOrganizations.length, 0);
                    done();
                });
        });

        it('should return organization details with empty associated org array when associated orgs are not found', (done) => {
            getStub.resolves({ status: 'active', isVerified: 1, role: 4, accessLevel: CONSTANTS.ACCESS_LEVEL.ROOT });

            sinon.stub(Organization, 'get').resolves({ ...organization, associatedOrganizations: null });
            sinon.stub(Organization, 'batchGet').resolves([]);

            request(process.env.BASE_URL)
                .get('/organization/associatedOrganizations')
                .query({ orgId: '*************-4b2e-b36b-898597b8146f' })
                .set({ Authorization: requestPayloadUser.token })
                .end((err, res) => {
                    expect(res.body.status).to.be.status;
                    assert.equal(res.statusCode, 200);
                    assert.equal(res.body.data.associatedOrganizations.length, 0);
                    done();
                });
        });

        it('should return organization details with associated organizations', (done) => {
            getStub.resolves({ status: 'active', isVerified: 1, role: 4, accessLevel: CONSTANTS.ACCESS_LEVEL.ROOT });

            sinon.stub(Organization, 'get').resolves(organization);
            sinon.stub(Organization, 'batchGet').resolves([{ id: 'org-1' }, { id: 'org-2' }, { id: 'org-3' }]);

            request(process.env.BASE_URL)
                .get('/organization/associatedOrganizations')
                .query({ orgId: '*************-4b2e-b36b-898597b8146f' })
                .set({ Authorization: requestPayloadUser.token })
                .end((err, res) => {
                    expect(res.body.status).to.be.status;
                    assert.equal(res.statusCode, 200);
                    assert.equal(res.body.data.associatedOrganizations.length, 3);
                    done();
                });
        });
    } catch (error) {
        CONSOLE_LOGGER.error(error);
    }
});

describe('Add Associated Organizations', () => {
    try {
        let getStub;
        beforeEach(async () => {
            getStub = sinon.stub(User, 'get');
        });

        afterEach(() => {
            sinon.restore();
        });

        it('should throw error if organization with parent org id is not found', (done) => {
            getStub.resolves({ status: 'active', isVerified: 1, role: 4, accessLevel: CONSTANTS.ACCESS_LEVEL.ROOT });

            sinon.stub(Organization, 'get').resolves(null);

            request(process.env.BASE_URL)
                .post('/organization/associatedOrganizations')
                .send({
                    associatedOrgsDetails: [],
                    email: '<EMAIL>',
                    adminFirstName: 'Test',
                    adminLastName: 'Name',
                    parentOrgId: '*************-4b2e-b36b-898597b8146f'
                })
                .set({ Authorization: requestPayloadUser.token })
                .end((err, res) => {
                    expect(res.body.status).to.be.status;
                    assert.equal(res.statusCode, 400);
                    assert.equal(res.body.message, 'Organization with given id does not exist');
                    done();
                });
        });

        it('should return empty array if associated orgs details already exist', (done) => {
            getStub.resolves({ status: 'active', isVerified: 1, role: 4, accessLevel: CONSTANTS.ACCESS_LEVEL.ROOT });

            sinon.stub(Organization, 'get').resolves(organization);
            sinon.stub(Organization, 'query').returns({
                eq: sinon.stub().returns({
                    using: sinon.stub().returns({
                        where: sinon.stub().returns({
                            eq: sinon.stub().returns({
                                where: sinon.stub().returns({
                                    eq: sinon.stub().returns({
                                        exec: sinon.stub().resolves([{ id: 'org-1', name: 'Test Organization', zipCode: '12345' }])
                                    })
                                })
                            })
                        })
                    })
                })
            });

            request(process.env.BASE_URL)
                .post('/organization/associatedOrganizations')
                .send({
                    associatedOrgsDetails: [
                        {
                            name: 'Test Organization',
                            zipCode: '12345',
                            allowedPaymentType: {
                                cash: false,
                                cheque: true,
                                stripe: false,
                                venmo: false
                            },
                            paymentInstructions: {
                                chequeInstructions: ''
                            },
                            category: 'Homeroom',
                            address: 'Organization test address',
                            country: 'country',
                            city: 'city',
                            state: 'state',
                            parentOrganization: '*************-4b2e-b36b-898597b8146f'
                        }
                    ],
                    email: '<EMAIL>',
                    adminFirstName: 'Test',
                    adminLastName: 'Name',
                    parentOrgId: '*************-4b2e-b36b-898597b8146f'
                })
                .set({ Authorization: requestPayloadUser.token })
                .end((err, res) => {
                    expect(res.body.status).to.be.status;
                    assert.equal(res.statusCode, 200);
                    assert.equal(res.body.data.length, 0);
                    done();
                });
        });

        it('should return associated org details that are not present in the database', (done) => {
            getStub.resolves({ status: 'active', isVerified: 1, role: 4, accessLevel: CONSTANTS.ACCESS_LEVEL.ROOT });

            sinon.stub(Organization, 'get').resolves(organization);
            sinon.stub(Organization, 'query').returns({
                eq: sinon.stub().returns({
                    using: sinon.stub().returns({
                        where: sinon.stub().returns({
                            eq: sinon.stub().returns({
                                where: sinon.stub().returns({
                                    eq: sinon.stub().returns({
                                        exec: sinon.stub().resolves([])
                                    })
                                })
                            })
                        })
                    })
                })
            });

            sinon.stub(Organization, 'batchPut').resolves();
            sinon.stub(User, 'batchPut').resolves();
            sinon.stub(organizationMemberModel, 'batchPut').resolves();

            sinon.stub(User, 'query').returns({
                eq: sinon.stub().returns({
                    using: sinon.stub().returns({
                        exec: sinon.stub().resolves([{ id: 'user-1', associatedOrganizations: ['org-1'] }])
                    })
                })
            });

            sinon.stub(Organization, 'batchGet').resolves([{ id: 'org-1' }]);

            request(process.env.BASE_URL)
                .post('/organization/associatedOrganizations')
                .send({
                    associatedOrgsDetails: [
                        {
                            name: 'Test Organization',
                            zipCode: '12345',
                            allowedPaymentType: {
                                cash: false,
                                cheque: true,
                                stripe: false,
                                venmo: false
                            },
                            paymentInstructions: {
                                chequeInstructions: ''
                            },
                            category: 'Homeroom',
                            address: 'Organization test address',
                            country: 'country',
                            city: 'city',
                            state: 'state',
                            parentOrganization: '*************-4b2e-b36b-898597b8146f'
                        }
                    ],
                    email: '<EMAIL>',
                    adminFirstName: 'Test',
                    adminLastName: 'Name',
                    parentOrgId: '*************-4b2e-b36b-898597b8146f'
                })
                .set({ Authorization: requestPayloadUser.token })
                .end((err, res) => {
                    expect(res.body.status).to.be.status;
                    assert.equal(res.statusCode, 200);
                    assert.equal(res.body.data.length, 1);
                    assert.equal(res.body.data[0].name, 'Test Organization');
                    assert.equal(res.body.data[0].zipCode, '12345');
                    done();
                });
        });
    } catch (error) {
        CONSOLE_LOGGER.error(error);
    }
});
